package com.projectkorra.projectkorra.region;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.kingdoms.constants.land.Land;
import org.kingdoms.constants.player.KingdomPlayer;

import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;

class KingdomsX extends RegionProtectionBase {

  protected KingdomsX() {
    super("Kingdoms", "Kingdoms.Respect");
  }

  @Override
  public boolean isRegionProtectedReal(Player player, Location location, CoreAbility ability, boolean igniteAbility, boolean explosiveAbility) {
    try {
      if (player.hasPermission("kingdoms.bypass")) {
        return false;
      }

      boolean protectDuringInvasions = ConfigManager.defaultConfig.get().getBoolean("Properties.RegionProtection.Kingdoms.ProtectDuringInvasions");

      Land land = Land.getLand(location);

      if (land == null || !land.isClaimed()) {
        return false;
      }

      if (!protectDuringInvasions && land.getKingdom().isBeingInvaded()) {
        return false;
      }

      KingdomPlayer kPlayer = KingdomPlayer.getKingdomPlayer(player);

      if (kPlayer == null) {
        return true;
      }

      if (kPlayer.hasKingdom() && land.getKingdom().equals(kPlayer.getKingdom())) {
        return false;
      }

      return true;

    } catch (Exception e) {
      return false;
    }
  }
}
